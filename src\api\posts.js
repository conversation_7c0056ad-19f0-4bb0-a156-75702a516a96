const express = require('express');
const { Post, UserPost } = require('../db/models');

const router = express.Router();


// Get blog posts by author IDs with filtering and sorting
router.get('/', async (req, res, next) => {
  try {
    // Authentication check
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { authorIds, sortBy = 'id', direction = 'asc' } = req.query;

    // Validate required authorIds parameter
    if (!authorIds) {
      return res.status(400).json({ error: 'authorIds parameter is required' });
    }

    // Validate sortBy parameter
    const validSortFields = ['id', 'reads', 'likes', 'popularity'];
    if (!validSortFields.includes(sortBy)) {
      return res.status(400).json({
        error: 'sortBy must be one of: id, reads, likes, popularity'
      });
    }

    // Validate direction parameter
    const validDirections = ['asc', 'desc'];
    if (!validDirections.includes(direction)) {
      return res.status(400).json({
        error: 'direction must be either asc or desc'
      });
    }

    // Parse authorIds and validate they are integers
    let authorIdArray;
    try {
      authorIdArray = authorIds.split(',').map(id => {
        const parsedId = parseInt(id.trim(), 10);
        if (isNaN(parsedId)) {
          throw new Error(`Invalid author ID: ${id}`);
        }
        return parsedId;
      });
    } catch (error) {
      return res.status(400).json({
        error: 'authorIds must be a comma-separated list of integers'
      });
    }

    // Fetch posts for each author ID
    const allPosts = [];
    for (const authorId of authorIdArray) {
      try {
        const posts = await Post.getPostsByUserId(authorId);
        allPosts.push(...posts);
      } catch (error) {
        // Continue with other authors if one fails
        console.error(`Error fetching posts for author ${authorId}:`, error);
      }
    }

    // Remove duplicates efficiently using a Map
    const uniquePostsMap = new Map();
    allPosts.forEach(post => {
      uniquePostsMap.set(post.id, post);
    });

    // Convert to array and transform data
    let posts = Array.from(uniquePostsMap.values()).map(post => {
      const postData = post.toJSON ? post.toJSON() : post;
      return {
        id: postData.id,
        likes: postData.likes,
        popularity: postData.popularity,
        reads: postData.reads,
        tags: postData.tags ? postData.tags.split(',').map(tag => tag.trim()) : [],
        text: postData.text
      };
    });

    // Sort posts
    posts.sort((a, b) => {
      let comparison = 0;
      if (a[sortBy] < b[sortBy]) {
        comparison = -1;
      } else if (a[sortBy] > b[sortBy]) {
        comparison = 1;
      }

      return direction === 'desc' ? -comparison : comparison;
    });

    res.json({ posts });
  } catch (error) {
    next(error);
  }
});

/**
 * Create a new blog post
 * req.body is expected to contain {text: required(string), tags: optional(Array<string>)}
 */
router.post('/', async (req, res, next) => {
  try {
    // Validation
    if (!req.user) {
      return res.sendStatus(401);
    }

    const { text, tags } = req.body;

    if (!text) {
      return res
        .status(400)
        .json({ error: 'Must provide text for the new post' });
    }

    // Create new post
    const values = {
      text,
    };
    if (tags) {
      values.tags = tags.join(',');
    }
    const post = await Post.create(values);
    await UserPost.create({
      userId: req.user.id,
      postId: post.id,
    });

    res.json({ post });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
