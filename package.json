{"name": "blog-authors-be-node", "version": "1.0.0", "description": "A back-end node.js work simulation", "main": "index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "lint": "eslint --fix src", "test": "cross-env SESSION_SECRET=notsecure jest --injectGlobals=false --forceExit", "seed": "node src/db/seed.js", "prettier": "prettier src --write"}, "keywords": [], "author": "", "repository": {"type": "git", "url": ""}, "license": "", "dependencies": {"bcrypt": "^5.0.1", "cors": "^2.8.5", "dotenv": "^10.0.0", "express": "^4.18.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "sequelize": "^6.35.0", "sqlite3": "^5.0.2"}, "devDependencies": {"@jest/globals": "^29.7.0", "cross-env": "^7.0.3", "eslint": "^8.15.0", "jest": "^29.7.0", "nodemon": "^2.0.7", "prettier": "^2.6.2", "supertest": "^6.3.3"}}